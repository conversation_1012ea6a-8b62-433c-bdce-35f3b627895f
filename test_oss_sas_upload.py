import asyncio
import os
from pathlib import Path
from urllib import request
from utils import Tools, load_yaml_config
import aiohttp
from io import BytesIO

async def test_oss_sas_upload():
    # 加载配置
    config_path = Path(__file__).parent / "config" / "setting.yaml"
    Tools.conf = await load_yaml_config(config_path)
    
    # 导入需要在配置加载后进行
    from control.upload import build_oss_sas_url, upload_oss_with_sas
    
    # 创建测试数据
    test_content = "Hello, this is a test file for OSS SAS token upload!"
    test_data = BytesIO(test_content.encode('utf-8'))
    test_filename = "oss_sas_test.txt"
    
    print("开始测试OSS SAS令牌上传...")
    
    try:
        # 1. 获取OSS签名URL
        sas_url, headers = await build_oss_sas_url(
            "test", 
            test_filename,
            headers={'content_type': 'text/plain'}
        )
        print(f"生成的OSS签名URL: {sas_url}")
        print(f"使用的headers: {headers}")
        
        # 2. 使用签名URL上传
        result_url = await upload_oss_with_sas(
            data=test_data.getvalue(),
            pre="test",
            file_name=test_filename,
            headers={'content_type': 'text/plain'}
        )
        print(result_url)
        print(f"OSS SAS上传成功！")
        print(f"文件访问URL: {result_url}")
        
        # 3. 验证文件是否可访问
        print("\n开始验证文件...")
        async with aiohttp.ClientSession() as session:
            async with session.get(result_url) as response:
                if response.status == 200:
                    content = await response.read()
                    print("文件验证成功！")
                    print(f"文件内容: {content.decode('utf-8')}")
                else:
                    print(f"文件访问失败，状态码: {response.status}")
        
    except Exception as e:
        print(f"上传过程中发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(test_oss_sas_upload()) 