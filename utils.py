# -*- coding: utf-8 -*-
# @Time    : 2023/7/14 8:28
# <AUTHOR> hzx1994
# @File    : utils.py
# @Software: PyCharm
# @description:
from pathlib import Path
from datetime import datetime, timedelta
from azure.storage.blob import generate_blob_sas, BlobSasPermissions

from config import config
from typing import Union




class Tools:
    conf: config.Config




async def load_yaml_config(file_path: Union[str, Path]) -> config.Config:
    conf = await config.load_yaml_config(file_path)
    return conf

def parse_connection_string(conn_str: str) -> dict:
    """
    解析Azure存储连接字符串
    
    Args:
        conn_str (str): Azure存储连接字符串
        
    Returns:
        dict: 解析后的配置字典
    """
    if not conn_str:
        raise ValueError("连接字符串不能为空")
        
    parts = [p for p in conn_str.split(';') if p]  # 过滤空字符串
    config = {}
    
    for part in parts:
        if '=' in part:
            key, value = part.split('=', 1)
            key = key.strip()
            value = value.strip()
            if key and value:  # 只保存非空的键值对
                config[key] = value
                
    # 确保必要的字段存在
    required_fields = ['AccountName', 'AccountKey']
    missing_fields = [field for field in required_fields if field not in config]
    if missing_fields:
        raise ValueError(f"连接字符串缺少必要字段: {', '.join(missing_fields)}")
        
    return config

def generate_blob_sas_token(blob_name: str, container_name: str = None) -> str:
    """
    生成Azure Blob Storage的SAS (共享访问签名) 令牌
    
    Args:
        blob_name (str): Blob对象的名称
        container_name (str, optional): 容器名称，如果不指定则使用配置中的默认容器
        
    Returns:
        str: 完整的带SAS令牌的URL
    """
    if not blob_name:
        raise ValueError("blob_name不能为空")
        
    if not container_name:
        container_name = Tools.conf.blob.container_name
        if not container_name:
            raise ValueError("container_name不能为空")
    
    # 从连接字符串中解析配置
    conn_config = parse_connection_string(Tools.conf.blob.connect_str)
    account_key = conn_config['AccountKey']  # 现在可以直接使用字典索引，因为已经确保字段存在
    account_name = conn_config['AccountName']
    endpoint_suffix = conn_config.get('EndpointSuffix', 'core.windows.net')
    
    # 构建account_url
    account_url = f"https://{account_name}.blob.{endpoint_suffix}"
        
    # 设置SAS令牌的权限和有效期
    permissions = BlobSasPermissions(
        read=True,      # 允许读取
        write=True,     # 允许写入
        create=True,    # 允许创建
        delete=False,   # 不允许删除
        add=False,      # 不允许添加块
        delete_version=False,  # 不允许删除版本
        list=False,     # 不允许列出
        move=False,     # 不允许移动
        execute=False,  # 不允许执行
        set_immutability_policy=False,  # 不允许设置不可变策略
        permanent_delete=False,  # 不允许永久删除
        tag=False       # 不允许标记
    )
    
    # 设置令牌的有效期（从现在开始1小时）
    start_time = datetime.utcnow()
    expiry_time = start_time + timedelta(hours=1)
    
    try:
        # 生成SAS令牌
        sas_token = generate_blob_sas(
            account_name=account_name,
            container_name=container_name,
            blob_name=blob_name,
            account_key=account_key,
            permission=permissions,
            expiry=expiry_time,
            start=start_time
        )
    except Exception as e:
        raise ValueError(f"生成SAS令牌失败: {str(e)}")
    account_url = f"https://file.302.ai"
    # 构建完整的URL
    sas_url = f"{account_url}/{container_name}/{blob_name}?{sas_token}"
    return sas_url
