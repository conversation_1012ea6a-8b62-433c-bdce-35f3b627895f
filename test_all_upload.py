import asyncio
import os
from pathlib import Path
from utils import Tools, load_yaml_config
import aiohttp
from io import BytesIO
import oss2
from datetime import datetime, timedelta

async def test_blob_sas_upload():
    """测试Azure Blob Storage的SAS令牌上传"""
    from control.upload import upload_file, build_blob_sas_url
    
    # 创建测试数据
    test_content = "Hello, this is a test file for Blob SAS token upload!"
    test_data = BytesIO(test_content.encode('utf-8'))
    test_filename = "blob_test.txt"
    
    print("\n开始测试Blob SAS令牌上传...")
    
    try:
        # 1. 先获取SAS URL
        sas_url, headers = await build_blob_sas_url("test", test_filename)
        print(f"生成的Blob SAS URL: {sas_url}")
        print(f"使用的headers: {headers}")
        
        # 2. 使用SAS令牌上传
        result = await upload_file(
            prefix="test",
            file_name=test_filename,
            data=test_data,
            use_sas=True
        )
        print(f"Blob SAS上传成功！")
        print(f"文件访问URL: {result['url']}")
        
    except Exception as e:
        print(f"Blob上传错误: {str(e)}")
        import traceback
        print(traceback.format_exc())

async def test_oss_upload():
    """测试阿里云OSS的上传"""
    from control.upload import upload_file
    
    # 创建测试数据
    test_content = "Hello, this is a test file for OSS upload!"
    test_data = BytesIO(test_content.encode('utf-8'))
    test_filename = "oss_test.txt"
    
    print("\n开始测试OSS上传...")
    
    try:
        # 使用传统方式上传
        result = await upload_file(
            prefix="test",
            file_name=test_filename,
            data=test_data,
            use_sas=False
        )
        print(f"OSS上传成功！")
        print(f"OSS URL: {result['oss_url']}")
        
        # 验证文件是否可访问
        async with aiohttp.ClientSession() as session:
            async with session.get(result['oss_url']) as response:
                if response.status == 200:
                    print("OSS文件可以正常访问")
                else:
                    print(f"OSS文件访问失败，状态码: {response.status}")
        
    except Exception as e:
        print(f"OSS上传错误: {str(e)}")
        import traceback
        print(traceback.format_exc())

async def test_traditional_upload():
    """测试传统方式的上传（同时上传到OSS和Blob）"""
    from control.upload import upload_file
    
    # 创建测试数据
    test_content = "Hello, this is a test file for traditional upload!"
    test_data = BytesIO(test_content.encode('utf-8'))
    test_filename = "traditional_test.txt"
    
    print("\n开始测试传统方式上传...")
    
    try:
        result = await upload_file(
            prefix="test",
            file_name=test_filename,
            data=test_data,
            use_sas=False
        )
        print("传统方式上传成功！")
        print(f"OSS URL: {result['oss_url']}")
        print(f"Blob URL: {result['blob_url']}")
        
    except Exception as e:
        print(f"传统上传错误: {str(e)}")
        import traceback
        print(traceback.format_exc())

async def run_all_tests():
    # 加载配置
    config_path = Path(__file__).parent / "config" / "setting.yaml"
    Tools.conf = await load_yaml_config(config_path)
    
    # 运行所有测试
    print("=== 开始运行所有上传测试 ===")
    
    await test_blob_sas_upload()
    await test_oss_upload()
    await test_traditional_upload()
    
    print("\n=== 所有测试完成 ===")

if __name__ == "__main__":
    asyncio.run(run_all_tests()) 