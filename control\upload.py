from argparse import FileType
import asyncio
import hashlib
import mimetypes
import os
import time
from urllib.parse import urlparse
import aiohttp
from azure.storage.blob import BlobServiceClient, ContentSettings
import hashlib
import oss2
from datetime import datetime, timedelta

from utils import Tools, generate_blob_sas_token
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
class OSSClientPool:
    def __init__(self, size=5):
        self.pool = asyncio.Queue(maxsize=size)
        self.size = size
        self._initialize_pool()

    def _initialize_pool(self):
        for _ in range(self.size):
            auth = oss2.Auth(Tools.conf.oss2.access_key_id, Tools.conf.oss2.access_key_secret)
            bucket = oss2.Bucket(auth, Tools.conf.oss2.endpoint, Tools.conf.oss2.bucket_name, is_cname=False,connect_timeout=300)
            self.pool.put_nowait(bucket)

    async def get_client(self):
        return await self.pool.get()

    async def release_client(self, client):
        await self.pool.put(client)
class BlobClientPool:
    def __init__(self, size=5):
        self.pool = asyncio.Queue(maxsize=size)
        self.size = size
        self._initialize_pool()

    def _initialize_pool(self):
        for _ in range(self.size):
            client = BlobServiceClient.from_connection_string(Tools.conf.blob.connect_str)
            self.pool.put_nowait(client)

    async def get_client(self):
        return await self.pool.get()

    async def release_client(self, client):
        await self.pool.put(client)

# Initialize the pool
blob_client_pool = BlobClientPool(size=20)
oss_client_pool = OSSClientPool(size=20)

async def download_file(url: str, session: aiohttp.ClientSession):
    logger.info(f"Downloading file from {url}")
    async with session.get(url,ssl=False) as response:
        return await response.read()



def get_content_type(file_extension):
    # 使用 mimetypes 模块来猜测 MIME 类型
    mime_type, _ = mimetypes.guess_type(f"file.{file_extension}")
    return mime_type


def get_file_extension_from_content_type(content_type: str) -> str:
    extension = mimetypes.guess_extension(content_type)
    if extension:
        return "."+extension
    return None

def get_file_extension_from_url(url):
    # 解析url上的文件后缀，大概
    parsed_url = urlparse(url)
    file_path = parsed_url.path
    _, extension = os.path.splitext(file_path)
    if extension == '.image':
        extension = '.png'
    return extension

def get_file_extension_from_data(data)->tuple[str,str]:
    # 最优先
    try:
        import filetype
        kind = filetype.guess(data)
        if kind:
            return "."+kind.extension, kind.mime
    except:
        pass
    return None, None

def get_file_extension_mime(content_type: str = None, data: bytes = None) -> tuple[str, str]:
    if data:
        return get_file_extension_from_data(data)
    if content_type:
        return get_file_extension_from_content_type(content_type), content_type
    return None, None

async def get_url_headers(url: str, session: aiohttp.ClientSession) -> dict:
    headers = {}
    async with session.head(url) as response:
        headers["content_type"] = response.headers.get("Content-Type")
        headers["content_length"] = response.headers.get("Content-Length")
        headers["content_encoding"] = response.headers.get("Content-Encoding")
    return headers

def generate_file_name(data:bytes,extension:str="")->str:
    file_name = hashlib.md5(data).hexdigest()
    if extension:
        file_name = file_name+extension
    return file_name

async def check_url_exists(url: str)->bool:
    timeout = aiohttp.ClientTimeout(total=30, connect=10)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.head(url) as response:
            return response.status == 200


from urllib.parse import urlparse
import os

def get_filename_from_url(url):
    # 解析 URL
    parsed_url = urlparse(url)
    # 从路径中获取文件名
    file_name = os.path.basename(parsed_url.path)
    return file_name



async def get_file_name(url: str="", data=None, file_name: str = None)->tuple[bytes,str,dict]:
    headers = {}
    file_data = None

    if url:
        # 配置超时设置，增加读取超时时间
        timeout = aiohttp.ClientTimeout(
            total=300,  # 总超时时间5分钟
            connect=30,  # 连接超时30秒
            sock_read=120  # 读取超时2分钟
        )
        async with aiohttp.ClientSession(timeout=timeout) as session:
            file_data = await download_file(url,session=session)
            url_headers = await get_url_headers(url,session=session)
            # 只保留有效的头部信息
            headers.update({
                k: v for k, v in url_headers.items()
                if k is not None and v is not None
            })
            file_name = get_filename_from_url(url)
            
    elif data:
        if hasattr(data, 'read'):
            file_data = data.read() if not asyncio.iscoroutinefunction(data.read) else await data.read()
            if hasattr(data, 'seek'):
                await data.seek(0)
        else:
            file_data = data
            
    if file_data:
        extension, mime = get_file_extension_from_data(file_data)
        if mime:
            headers["content_type"] = mime
        if not headers.get("content_type"):
            extension, mime = get_file_extension_mime(data=file_data)
            if mime:
                headers["content_type"] = mime
        if not file_name:
            file_name = generate_file_name(file_data, extension)
            
    # 确保content_type存在且有效
    if not headers.get("content_type"):
        headers["content_type"] = "application/octet-stream"
            
    return file_data, file_name, headers


async def upload_oss(data,pre,file_name,headers):
    st = time.time()
    loop = asyncio.get_running_loop()

    bucket = await oss_client_pool.get_client()
    bucket.timeout = 300
    bucket.
    # job2 = loop.run_in_executor(executor,bucket.put_object,object_name, bytes_data)
    try:
        await loop.run_in_executor(None, bucket.put_object, f"gpt/{pre}/{file_name}", data, {'Content-Type': headers.get("content_type")})
    finally:
        # Release the client back to the pool
        await oss_client_pool.release_client(bucket)

    url = f"https://file.302ai.cn/gpt/{pre}/{file_name}"
    logger.info(f"OSS upload {url} cost {time.time()-st}")
    return url


async def upload_blob_from_url(data: bytes, pre: str = 'imgs', file_name: str = None, headers: dict = None):
    st = time.time()
    container_name = "gpt"

    url = f"https://file.302.ai/gpt/{pre}/{file_name}"
    if not await check_url_exists(url):
        blob_service_client = await blob_client_pool.get_client()
        try:
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=f'{pre}/{file_name}')
            loop = asyncio.get_event_loop()
            func = lambda: blob_client.upload_blob(data, overwrite=True, content_settings=ContentSettings(**headers))
            await loop.run_in_executor(None, func)
        finally:
            # Release the client back to the pool
            await blob_client_pool.release_client(blob_service_client)
    logger.info(f"Blob upload {url} cost {time.time()-st}")
    return url

async def build_blob_sas_url(pre,file_name):
    container_name = "gpt"
    blob_path = f'{pre}/{file_name}'
    
    # 生成带SAS令牌的URL，同时获取account_url
    sas_url = generate_blob_sas_token(blob_path, container_name)
    return sas_url,{'x-ms-blob-type': 'BlockBlob'}

async def upload_blob_with_sas(data: bytes, pre: str = 'imgs', file_name: str = None, headers: dict = None):
    """
    使用SAS令牌上传文件到Azure Blob Storage
    """
    container_name = "gpt"
    blob_path = f'{pre}/{file_name}'
    
    # 生成带SAS令牌的URL，同时获取account_url
    sas_url = generate_blob_sas_token(blob_path, container_name)
    
    # 从SAS URL中提取base URL (去掉查询参数)
    base_url = sas_url.split('?')[0]
    
    # 处理headers，确保所有键值都是有效的字符串
    clean_headers = {'x-ms-blob-type': 'BlockBlob'}
    if headers:
        # 只保留有效的键值对
        clean_headers.update({
            str(k): str(v) 
            for k, v in headers.items() 
            if k is not None and v is not None
        })
    
    # 使用aiohttp直接上传到带SAS令牌的URL
    timeout = aiohttp.ClientTimeout(total=300, connect=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.put(sas_url, data=data, headers=clean_headers) as response:
            if response.status not in (201, 200):
                raise Exception(f"Upload failed with status {response.status}")
    
    # 返回不带SAS令牌的URL
    return base_url

async def build_oss_sas_url(pre, file_name, headers=None):
    """
    生成OSS的SAS URL
    
    Args:
        pre: 文件前缀
        file_name: 文件名
        headers: 上传时需要的headers
    """
    auth = oss2.Auth(Tools.conf.oss2.access_key_id, Tools.conf.oss2.access_key_secret)
    bucket = oss2.Bucket(auth, Tools.conf.oss2.endpoint, Tools.conf.oss2.bucket_name)
    
    # 设置签名URL的有效期（1小时）
    expiration = int((datetime.utcnow() + timedelta(hours=1)).timestamp())
    
    # 准备headers
    headers = headers or {}
    content_type = headers.get('content_type', 'application/octet-stream')
    
    # 设置请求头
    request_headers = {
        'Content-Type': content_type,
        'x-oss-storage-class': 'Standard',
    }
    
    # 生成签名URL，添加必要的请求头
    object_name = f"gpt/{pre}/{file_name}"
    url = bucket.sign_url('PUT', object_name, expiration, headers=request_headers)
    url = url.replace("http://302ai.oss-cn-shenzhen.aliyuncs.com","https://file.302ai.cn")
    return url, request_headers

async def upload_oss_with_sas(data: bytes, pre: str = 'imgs', file_name: str = None, headers: dict = None):
    """
    使用SAS URL上传文件到OSS
    """
    # 生成签名URL和对应的headers
    sas_url, request_headers = await build_oss_sas_url(pre, file_name, headers)
    
    # 处理headers，合并请求头
    clean_headers = request_headers.copy()
    if headers:
        clean_headers.update({
            str(k): str(v) 
            for k, v in headers.items() 
            if k is not None and v is not None
        })
    
    # 使用aiohttp上传
    timeout = aiohttp.ClientTimeout(total=300, connect=30)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.put(sas_url, data=data, headers=clean_headers) as response:
            if response.status not in (200, 201):
                error_text = await response.text()
                raise Exception(f"OSS Upload failed with status {response.status}: {error_text}")
    
    # 返回可访问的URL
    return f"https://file.302ai.cn/gpt/{pre}/{file_name}"


import os
import mimetypes

def get_file_extension_and_content_type(filename):
    # 获取文件的扩展名
    _, file_extension = os.path.splitext(filename)
    
    # 获取对应的 MIME 类型
    content_type, _ = mimetypes.guess_type(filename)
    if file_extension.endswith("glb"):
        content_type = "model/gltf-binary"
    
    return file_extension, content_type


async def upload_file(prefix, file_name, url: str = "", data=None, blob_url=True, use_sas=False):
    """
    上传文件到存储服务
    
    Args:
        prefix: 文件前缀路径
        file_name: 文件名
        url: 可选，从URL下载文件并上传
        data: 可选，直接上传的文件数据（bytes或支持read的对象）
        blob_url: 是否使用blob url
        use_sas: 是否使用SAS令牌上传
    """
    if file_name and data:
         file_extension, content_type = get_file_extension_and_content_type(file_name)
         headers = {}
         headers["content_type"] = content_type
         file_data = data.read() if not asyncio.iscoroutinefunction(data.read) else await data.read()
         file_name = generate_file_name(file_data, file_extension)
    else:
        file_data, file_name, headers = await get_file_name(url, data=data, file_name=file_name)
    
    if not file_data:
        raise ValueError("没有提供有效的文件数据")
    
    if use_sas:
        # 使用SAS令牌同时上传到OSS和Blob
        oss_url, blob_url = await asyncio.gather(
            upload_oss_with_sas(file_data, prefix, file_name, headers),
            upload_blob_with_sas(file_data, prefix, file_name, headers)
        )
        return {"oss_url": oss_url, "blob_url": blob_url, "url": blob_url}
    
    # 原有的上传逻辑
    oss_url, blob_url = await asyncio.gather(
        upload_oss(file_data, prefix, file_name, headers),
        upload_blob_from_url(file_data, prefix, file_name, headers)
    )
    return {"oss_url": oss_url, "blob_url": blob_url, "url": blob_url if blob_url else oss_url}

async def delete_oss_file(file_path: str):
    """
    从OSS删除文件

    Args:
        file_path: 文件路径，格式如 "gpt/imgs/filename.jpg"

    Returns:
        bool: 删除是否成功
    """
    try:
        bucket = await oss_client_pool.get_client()
        try:
            loop = asyncio.get_running_loop()
            # 使用线程池执行删除操作
            await loop.run_in_executor(None, bucket.delete_object, file_path)
            logger.info(f"Successfully deleted OSS file: {file_path}")
            return True
        finally:
            await oss_client_pool.release_client(bucket)
    except Exception as e:
        logger.error(f"Failed to delete OSS file {file_path}: {str(e)}")
        return False

async def delete_blob_file(file_path: str):
    """
    从Azure Blob Storage删除文件

    Args:
        file_path: 文件路径，格式如 "imgs/filename.jpg"

    Returns:
        bool: 删除是否成功
    """
    try:
        container_name = "gpt"
        blob_service_client = await blob_client_pool.get_client()
        try:
            blob_client = blob_service_client.get_blob_client(container=container_name, blob=file_path)
            loop = asyncio.get_event_loop()
            # 使用线程池执行删除操作
            await loop.run_in_executor(None, blob_client.delete_blob)
            logger.info(f"Successfully deleted Blob file: {file_path}")
            return True
        finally:
            await blob_client_pool.release_client(blob_service_client)
    except Exception as e:
        logger.error(f"Failed to delete Blob file {file_path}: {str(e)}")
        return False

async def delete_file(file_path: str):
    """
    同时从OSS和Blob Storage删除文件

    Args:
        file_path: 文件路径，可以是以下格式之一：
                  - "gpt/imgs/filename.jpg" (完整路径)
                  - "imgs/filename.jpg" (相对路径，会自动添加gpt前缀)
                  - 完整URL (会自动解析路径)

    Returns:
        dict: 删除结果，包含oss_success和blob_success字段
    """
    # 处理不同格式的文件路径
    processed_path = _process_file_path(file_path)

    # 为OSS准备路径（包含gpt前缀）
    oss_path = processed_path if processed_path.startswith('gpt/') else f'gpt/{processed_path}'

    # 为Blob准备路径（不包含gpt前缀）
    blob_path = processed_path.replace('gpt/', '', 1) if processed_path.startswith('gpt/') else processed_path

    # 并发删除OSS和Blob中的文件
    oss_result, blob_result = await asyncio.gather(
        delete_oss_file(oss_path),
        delete_blob_file(blob_path),
        return_exceptions=True
    )

    # 处理异常结果
    oss_success = oss_result if isinstance(oss_result, bool) else False
    blob_success = blob_result if isinstance(blob_result, bool) else False

    if isinstance(oss_result, Exception):
        logger.error(f"OSS deletion failed with exception: {str(oss_result)}")
    if isinstance(blob_result, Exception):
        logger.error(f"Blob deletion failed with exception: {str(blob_result)}")

    result = {
        "oss_success": oss_success,
        "blob_success": blob_success,
        "overall_success": oss_success and blob_success,
        "file_path": processed_path
    }

    logger.info(f"File deletion result for {processed_path}: {result}")
    return result

def _process_file_path(file_path: str) -> str:
    """
    处理文件路径，支持多种输入格式

    Args:
        file_path: 输入的文件路径

    Returns:
        str: 处理后的标准路径
    """
    if not file_path:
        raise ValueError("文件路径不能为空")

    # 如果是完整URL，提取路径部分
    if file_path.startswith(('http://', 'https://')):
        parsed_url = urlparse(file_path)
        path = parsed_url.path.lstrip('/')

        # 处理不同域名的URL格式
        if 'file.302ai.cn' in file_path or 'file.302.ai' in file_path:
            # 从URL中提取gpt/后面的部分
            if '/gpt/' in path:
                return path
            else:
                raise ValueError(f"无法从URL中解析出有效的文件路径: {file_path}")
        else:
            return path

    # 如果已经是标准路径格式，直接返回
    return file_path.lstrip('/')

if __name__ == "__main__":
    # 测试代码示例
    pass
