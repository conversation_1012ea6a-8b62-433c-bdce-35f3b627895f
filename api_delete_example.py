#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件删除API示例
提供删除OSS和Blob Storage中文件的API接口
"""

import asyncio
import json
from typing import Dict, Any
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import logging

# 导入删除功能
from control.upload import delete_file
from utils import Tools, load_yaml_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="File Delete API", description="删除OSS和Blob Storage中的文件")

class DeleteRequest(BaseModel):
    """删除请求模型"""
    file_path: str
    
    class Config:
        schema_extra = {
            "example": {
                "file_path": "imgs/example.jpg"
            }
        }

class DeleteResponse(BaseModel):
    """删除响应模型"""
    success: bool
    message: str
    details: Dict[str, Any]

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化配置"""
    try:
        # 加载配置文件
        config_path = "config/setting.yaml"
        Tools.conf = await load_yaml_config(config_path)
        logger.info("Configuration loaded successfully")
    except Exception as e:
        logger.error(f"Failed to load configuration: {str(e)}")
        raise

@app.post("/delete", response_model=DeleteResponse)
async def delete_file_endpoint(request: DeleteRequest):
    """
    删除文件接口
    
    支持的文件路径格式：
    - "imgs/filename.jpg" (相对路径)
    - "gpt/imgs/filename.jpg" (完整路径)
    - "https://file.302ai.cn/gpt/imgs/filename.jpg" (完整URL)
    """
    try:
        logger.info(f"Received delete request for file: {request.file_path}")
        
        # 执行删除操作
        result = await delete_file(request.file_path)
        
        if result["overall_success"]:
            return DeleteResponse(
                success=True,
                message="文件删除成功",
                details=result
            )
        else:
            # 部分成功或完全失败
            success_parts = []
            failed_parts = []
            
            if result["oss_success"]:
                success_parts.append("OSS")
            else:
                failed_parts.append("OSS")
                
            if result["blob_success"]:
                success_parts.append("Blob Storage")
            else:
                failed_parts.append("Blob Storage")
            
            message = ""
            if success_parts:
                message += f"成功删除: {', '.join(success_parts)}. "
            if failed_parts:
                message += f"删除失败: {', '.join(failed_parts)}."
            
            return DeleteResponse(
                success=len(success_parts) > 0,  # 至少有一个成功就算部分成功
                message=message.strip(),
                details=result
            )
            
    except ValueError as e:
        logger.error(f"Invalid file path: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Unexpected error during file deletion: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "message": "File delete service is running"}

# 命令行使用示例
async def delete_file_cli(file_path: str):
    """
    命令行删除文件示例
    
    Args:
        file_path: 要删除的文件路径
    """
    try:
        # 加载配置
        config_path = "config/setting.yaml"
        Tools.conf = await load_yaml_config(config_path)
        
        # 执行删除
        result = await delete_file(file_path)
        
        print(f"删除结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result["overall_success"]:
            print("✅ 文件删除成功")
        else:
            print("⚠️ 文件删除部分成功或失败")
            
    except Exception as e:
        print(f"❌ 删除失败: {str(e)}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # 命令行模式
        file_path = sys.argv[1]
        asyncio.run(delete_file_cli(file_path))
    else:
        # API服务模式
        import uvicorn
        print("启动文件删除API服务...")
        print("使用示例:")
        print("  python api_delete_example.py imgs/example.jpg  # 命令行删除")
        print("  或直接运行启动API服务")
        uvicorn.run(app, host="0.0.0.0", port=8000)
