# -*- coding: utf-8 -*-
# @Time    : 2023/7/28 10:55
# <AUTHOR> hzx1994
# @File    : config.py
# @Software: PyCharm
# @description:
from pydantic import BaseModel
import yaml
from pathlib import Path
from typing import Optional

class OSS2Config(BaseModel):
    access_key_id: str
    access_key_secret: str
    endpoint: str
    bucket_name: str

class BlobConfig(BaseModel):
    container_name: str
    connect_str: str
    blob_name: Optional[str] = None

class Config(BaseModel):
    oss2: OSS2Config
    blob: BlobConfig

async def load_yaml_config(file_path: Path) -> Config:
    with open(file_path, 'r', encoding='utf-8') as f:
        config_dict = yaml.safe_load(f)
    return Config(**config_dict)