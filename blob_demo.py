import requests


with open("main.py", "rb") as file:
    data = file.read()

def get_blob_sas_url(pre,file_name):
    response = requests.get(f"https://upload.302ai.cn/blob/sas/url?pre={pre}&file_name={file_name}")
    """
    response :
    {
    "code": 0,
    "msg": "success",
    "data": {
        "sas_url": "https://proxyblob.blob.core.windows.net/gpt/imgs/test33.txt?st=2025-01-21T06%3A35%3A37Z&se=2025-01-21T07%3A35%3A37Z&sp=rcw&sv=2025-01-05&sr=b&sig=XrGZV10/XpEfDZMmyYGE6LZo5t61WYE3nKLeZW0uP%2Bo%3D",
        "headers": {
        "x-ms-blob-type": "BlockBlob"
        }
    }
    }
    """
    return response.json() 



def get_oss_sas_url(pre,file_name):
    response = requests.get(f"https://upload.302ai.cn/oss/sas/url?pre={pre}&file_name={file_name}")
    """
    response :
    {
    "code": 0,
    "msg": "success",
    "data": {
        "sas_url": "https://proxyblob.blob.core.windows.net/gpt/imgs/test33.txt?st=2025-01-21T06%3A35%3A37Z&se=2025-01-21T07%3A35%3A37Z&sp=rcw&sv=2025-01-05&sr=b&sig=XrGZV10/XpEfDZMmyYGE6LZo5t61WYE3nKLeZW0uP%2Bo%3D",
        "headers": {
        "x-ms-blob-type": "BlockBlob"
        }
    }
    }
    """
    return response.json() 



def upload_oss_with_sas(sas_url, data, clean_headers,is_blob=True):
    response = requests.put(sas_url, data=data, headers=clean_headers)
    if response.status_code not in (200, 201):
        error_text = response.text
        raise Exception(f"OSS Upload failed with status {response.status_code}: {error_text}")
    host = "https://proxyblob.blob.core.windows.net" if is_blob else "https://file.302ai.cn"
    # 返回可访问的URL
    return f"{host}/gpt/{pre}/{file_name}"

if __name__ == "__main__":
    pre = "imgs250"
    file_name = "test334.txt"
    res = get_blob_sas_url(pre,file_name)  # blob
    sas_url = res.get("data").get("sas_url")
    header = res.get("data").get("headers") 
    url = upload_oss_with_sas(sas_url, data, header,is_blob=True)
    print("blob url:",url)

    res = get_oss_sas_url(pre,file_name)  # oss
    sas_url = res.get("data").get("sas_url")
    header = res.get("data").get("headers") 
    url = upload_oss_with_sas(sas_url, data, header,is_blob=False)
    print("oss url:",url)