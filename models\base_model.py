# -*- coding: utf-8 -*-
# @Time    : 2023/7/28 10:01
# <AUTHOR> hzx1994
# @File    : base_model.py
# @Software: PyCharm
# @description:
from enum import Enum

from pydantic import BaseModel
from starlette.datastructures import State




class StateCode(int, Enum):
    SUCCESS = 0
    FAIL = 1
    AuthFail = 5001


class DB(BaseModel):
    host: str
    port: int
    database: str
    user: str
    password: str


class Redis(BaseModel):
    host: str
    port: int
    password: str=None

class Oss2(BaseModel):
    access_key_id: str
    access_key_secret: str
    endpoint: str
    bucket_name: str

class Blob(BaseModel):
    container_name: str
    connect_str: str

class Celery(BaseModel):
    broker: str
    backend: str


class Config(BaseModel):
    """
    配置文件
    """
    oss2: Oss2
    blob: Blob


class BaseData(BaseModel):
    code: int = 0
    msg: str = "success"
    data: dict = {}

