# 文件删除功能说明

本项目新增了文件删除功能，支持同时从 OSS 和 Azure Blob Storage 中删除文件。

## 功能特性

- ✅ 同时从 OSS 和 Blob Storage 删除文件
- ✅ 支持多种文件路径格式
- ✅ 异步并发删除，提高效率
- ✅ 详细的删除结果反馈
- ✅ 完善的错误处理和日志记录
- ✅ 连接池管理，提高性能

## 支持的文件路径格式

1. **相对路径**: `imgs/filename.jpg`
2. **完整路径**: `gpt/imgs/filename.jpg`
3. **完整URL**: `https://file.302ai.cn/gpt/imgs/filename.jpg`
4. **备用URL**: `https://file.302.ai/gpt/imgs/filename.jpg`

## 核心函数

### `delete_file(file_path: str)`

主要的删除函数，同时从 OSS 和 Blob Storage 删除文件。

**参数:**
- `file_path`: 文件路径（支持上述多种格式）

**返回值:**
```python
{
    "oss_success": bool,      # OSS删除是否成功
    "blob_success": bool,     # Blob删除是否成功
    "overall_success": bool,  # 整体是否成功（两者都成功）
    "file_path": str         # 处理后的标准路径
}
```

### `delete_oss_file(file_path: str)`

仅从 OSS 删除文件。

### `delete_blob_file(file_path: str)`

仅从 Azure Blob Storage 删除文件。

## 使用方法

### 1. 直接调用函数

```python
import asyncio
from control.upload import delete_file
from utils import Tools, load_yaml_config

async def main():
    # 加载配置
    Tools.conf = await load_yaml_config("config/setting.yaml")
    
    # 删除文件
    result = await delete_file("imgs/example.jpg")
    print(result)

asyncio.run(main())
```

### 2. 使用API接口

启动API服务：
```bash
python api_delete_example.py
```

发送删除请求：
```bash
curl -X POST "http://localhost:8000/delete" \
     -H "Content-Type: application/json" \
     -d '{"file_path": "imgs/example.jpg"}'
```

### 3. 命令行使用

```bash
# 直接删除文件
python api_delete_example.py imgs/example.jpg

# 或使用测试脚本
python test_delete.py imgs/example.jpg
```

### 4. 测试功能

```bash
# 运行删除功能测试
python test_delete.py test

# 运行完整上传删除流程测试
python test_delete.py full

# 启动交互式删除工具
python test_delete.py interactive
```

## API 接口文档

### POST /delete

删除指定文件。

**请求体:**
```json
{
    "file_path": "imgs/example.jpg"
}
```

**响应:**
```json
{
    "success": true,
    "message": "文件删除成功",
    "details": {
        "oss_success": true,
        "blob_success": true,
        "overall_success": true,
        "file_path": "imgs/example.jpg"
    }
}
```

### GET /health

健康检查接口。

**响应:**
```json
{
    "status": "healthy",
    "message": "File delete service is running"
}
```

## 错误处理

函数会处理以下错误情况：

1. **无效文件路径**: 抛出 `ValueError`
2. **网络错误**: 记录日志，返回失败状态
3. **权限错误**: 记录日志，返回失败状态
4. **文件不存在**: 记录日志，但不抛出异常

## 日志记录

所有删除操作都会记录详细日志：

- 成功删除: `INFO` 级别
- 删除失败: `ERROR` 级别
- 操作结果: `INFO` 级别

## 性能优化

- 使用连接池管理 OSS 和 Blob 客户端
- 异步并发删除，提高效率
- 线程池执行阻塞操作

## 注意事项

1. 确保配置文件 `config/setting.yaml` 正确配置了 OSS 和 Blob 的连接信息
2. 删除操作是不可逆的，请谨慎使用
3. 建议在生产环境中添加权限验证
4. 大批量删除时注意API限制

## 配置要求

确保 `config/setting.yaml` 包含以下配置：

```yaml
oss2:
  access_key_id: "your_oss_access_key_id"
  access_key_secret: "your_oss_access_key_secret"
  endpoint: "your_oss_endpoint"
  bucket_name: "your_oss_bucket_name"

blob:
  container_name: "gpt"
  connect_str: "your_azure_blob_connection_string"
```

## 依赖包

确保安装了以下依赖：

```bash
pip install aiohttp azure-storage-blob oss2 fastapi uvicorn pydantic
```
