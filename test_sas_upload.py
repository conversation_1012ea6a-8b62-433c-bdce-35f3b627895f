import asyncio
import os
from pathlib import Path
from utils import Tools, load_yaml_config
import aiohttp
from io import BytesIO

async def test_sas_upload():
    # 加载配置
    config_path = Path(__file__).parent / "config" / "setting.yaml"
    Tools.conf = await load_yaml_config(config_path)
    
    # 导入需要在配置加载后进行
    from control.upload import upload_file
    
    # 创建测试数据
    test_content = "Hello, this is a test file for SAS token upload!"
    test_data = BytesIO(test_content.encode('utf-8'))
    test_filename = "test22.txt"
    
    print("开始测试SAS令牌上传...")
    
    try:
        # 使用SAS令牌上传
        result = await upload_file(
            prefix="test2",
            file_name=test_filename,
            data=test_data,
            use_sas=True
        )
        print(f"使用SAS令牌上传成功！")
        print(f"文件访问URL: {result['url']}")
        
        # 为了对比，也测试传统方式上传
        # test_data.seek(0)  # 重置BytesIO的位置
        # result_traditional = await upload_file(
        #     prefix="test",
        #     file_name="test_traditional.txt",
        #     data=test_data,
        #     use_sas=False
        # )
        print("\n使用传统方式上传成功！")
        # print(f"OSS URL: {result_traditional['oss_url']}")
        # print(f"Blob URL: {result_traditional['blob_url']}")
        
    except Exception as e:
        print(f"上传过程中发生错误: {str(e)}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    asyncio.run(test_sas_upload()) 