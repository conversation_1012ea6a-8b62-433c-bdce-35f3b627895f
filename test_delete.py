#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件删除功能测试脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from control.upload import delete_file, upload_file
from utils import Tools, load_yaml_config

async def test_delete_functionality():
    """测试删除功能"""
    try:
        # 加载配置
        config_path = "config/setting.yaml"
        Tools.conf = await load_yaml_config(config_path)
        print("✅ 配置加载成功")
        
        # 测试不同格式的文件路径
        test_cases = [
            # 相对路径格式
            "imgs/test_file.jpg",
            
            # 完整路径格式
            "gpt/imgs/test_file.jpg",
            
            # URL格式
            "https://file.302ai.cn/gpt/imgs/test_file.jpg",
            "https://file.302.ai/gpt/imgs/test_file.jpg",
        ]
        
        print("\n🧪 测试不同格式的文件路径:")
        for i, file_path in enumerate(test_cases, 1):
            print(f"\n--- 测试用例 {i}: {file_path} ---")
            try:
                result = await delete_file(file_path)
                print(f"删除结果: {result}")
                
                if result["overall_success"]:
                    print("✅ 删除成功")
                elif result["oss_success"] or result["blob_success"]:
                    print("⚠️ 部分删除成功")
                else:
                    print("❌ 删除失败")
                    
            except Exception as e:
                print(f"❌ 测试失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 测试初始化失败: {str(e)}")

async def test_upload_and_delete():
    """测试上传后删除的完整流程"""
    try:
        # 加载配置
        config_path = "config/setting.yaml"
        Tools.conf = await load_yaml_config(config_path)
        print("✅ 配置加载成功")
        
        # 创建测试文件数据
        test_data = b"This is a test file for upload and delete functionality."
        test_filename = "test_upload_delete.txt"
        
        print(f"\n📤 上传测试文件: {test_filename}")
        
        # 上传文件
        upload_result = await upload_file(
            prefix="test",
            file_name=test_filename,
            data=test_data,
            use_sas=True
        )
        
        print(f"上传结果: {upload_result}")
        
        if upload_result.get("url"):
            print("✅ 文件上传成功")
            
            # 等待一下确保文件完全上传
            await asyncio.sleep(2)
            
            # 删除刚上传的文件
            print(f"\n🗑️ 删除测试文件: test/{test_filename}")
            delete_result = await delete_file(f"test/{test_filename}")
            
            print(f"删除结果: {delete_result}")
            
            if delete_result["overall_success"]:
                print("✅ 文件删除成功")
            else:
                print("⚠️ 文件删除部分成功或失败")
        else:
            print("❌ 文件上传失败，跳过删除测试")
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {str(e)}")

async def interactive_delete():
    """交互式删除功能"""
    try:
        # 加载配置
        config_path = "config/setting.yaml"
        Tools.conf = await load_yaml_config(config_path)
        print("✅ 配置加载成功")
        
        print("\n🔧 交互式文件删除工具")
        print("支持的路径格式:")
        print("  - imgs/filename.jpg (相对路径)")
        print("  - gpt/imgs/filename.jpg (完整路径)")
        print("  - https://file.302ai.cn/gpt/imgs/filename.jpg (完整URL)")
        print("输入 'quit' 退出")
        
        while True:
            file_path = input("\n请输入要删除的文件路径: ").strip()
            
            if file_path.lower() == 'quit':
                print("👋 再见!")
                break
                
            if not file_path:
                print("❌ 文件路径不能为空")
                continue
            
            try:
                print(f"🗑️ 正在删除文件: {file_path}")
                result = await delete_file(file_path)
                
                print(f"删除结果: {result}")
                
                if result["overall_success"]:
                    print("✅ 文件删除成功")
                elif result["oss_success"] or result["blob_success"]:
                    print("⚠️ 文件部分删除成功")
                    if result["oss_success"]:
                        print("  - OSS: 删除成功")
                    else:
                        print("  - OSS: 删除失败")
                    if result["blob_success"]:
                        print("  - Blob Storage: 删除成功")
                    else:
                        print("  - Blob Storage: 删除失败")
                else:
                    print("❌ 文件删除失败")
                    
            except Exception as e:
                print(f"❌ 删除失败: {str(e)}")
                
    except Exception as e:
        print(f"❌ 交互式删除工具初始化失败: {str(e)}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "test":
            print("🧪 运行删除功能测试...")
            asyncio.run(test_delete_functionality())
        elif command == "full":
            print("🔄 运行完整上传删除流程测试...")
            asyncio.run(test_upload_and_delete())
        elif command == "interactive":
            print("🔧 启动交互式删除工具...")
            asyncio.run(interactive_delete())
        else:
            # 直接删除指定文件
            file_path = sys.argv[1]
            print(f"🗑️ 删除文件: {file_path}")
            
            async def delete_single_file():
                config_path = "config/setting.yaml"
                Tools.conf = await load_yaml_config(config_path)
                result = await delete_file(file_path)
                print(f"删除结果: {result}")
                
            asyncio.run(delete_single_file())
    else:
        print("📖 文件删除测试脚本使用说明:")
        print("  python test_delete.py test          # 运行删除功能测试")
        print("  python test_delete.py full          # 运行完整上传删除流程测试")
        print("  python test_delete.py interactive   # 启动交互式删除工具")
        print("  python test_delete.py <file_path>   # 直接删除指定文件")

if __name__ == "__main__":
    main()
